import { useState } from 'react';
import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faGraduationCap,
  faHandshake,
  faUsers,
  faLaptopCode,
  faChalkboardTeacher,
  faArrowRight,
  faBrain,
  faRobot,
  faLink,
  faServer,
  faShieldAlt,
  faTasks,
  faComments,
  faPen,
  faVideo,
  faLightbulb,
  faExchangeAlt,
  faPeopleArrows,
  faUserFriends,
  faHandsHelping,
  faBalanceScale,
  faUniversalAccess,
  faChartLine,
  faGlobe
} from '@fortawesome/free-solid-svg-icons';

import Hero from '../../../components/ui/Hero';
import Section from '../../../components/ui/Section';
import Button from '../../../components/ui/Button';
import Card from '../../../components/ui/Card';

// Import placeholder images
import images from '../../../assets/placeholderImages';

const Home = () => {
  const [activeTab, setActiveTab] = useState('general');

  const services = [
    {
      icon: faGraduationCap,
      title: "Learn New Skills",
      description: "Discover and master new skills from expert instructors worldwide."
    },
    {
      icon: faChalkboardTeacher,
      title: "Teach Others",
      description: "Share your expertise and help others grow while earning income."
    },
    {
      icon: faHandshake,
      title: "Skill Exchange",
      description: "Trade skills directly with other users in our community."
    }
  ];

  const features = [
    "Expert-led courses and tutorials",
    "Interactive skill exchange platform",
    "Community-driven learning environment",
    "Flexible scheduling and pacing",
    "Verified skill certifications"
  ];

  const stats = [
    { number: "10K+", title: "Active Users" },
    { number: "500+", title: "Skills Available" },
    { number: "1K+", title: "Successful Exchanges" },
    { number: "95%", title: "Satisfaction Rate" }
  ];

  const testimonials = [
    {
      text: "SkillSwap transformed my career. I learned web development and now work as a full-stack developer!",
      author: "Sarah Johnson",
      role: "Web Developer"
    },
    {
      text: "Teaching on SkillSwap has been incredibly rewarding. I've helped hundreds of students while earning extra income.",
      author: "Michael Chen",
      role: "Data Science Instructor"
    },
    {
      text: "The skill exchange feature is amazing. I traded my design skills for programming lessons!",
      author: "Emily Rodriguez",
      role: "UX Designer"
    }
  ];

  const trendingSkills = [
    { name: "Web Development", count: "2.5k", description: "Build modern websites and applications" },
    { name: "Data Science", count: "1.8k", description: "Analyze data and build ML models" },
    { name: "Digital Marketing", count: "1.2k", description: "Master online marketing strategies" },
    { name: "Graphic Design", count: "980", description: "Create stunning visual designs" },
    { name: "Mobile Development", count: "750", description: "Build iOS and Android apps" },
    { name: "Photography", count: "650", description: "Capture and edit amazing photos" }
  ];

  const steps = [
    {
      number: "1",
      title: "Create Your Profile",
      description: "Sign up and showcase your skills and interests"
    },
    {
      number: "2",
      title: "Explore Skills",
      description: "Browse available courses or find exchange partners"
    },
    {
      number: "3",
      title: "Start Learning",
      description: "Begin your learning journey or teaching experience"
    },
    {
      number: "4",
      title: "Grow Together",
      description: "Build connections and advance your skills"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <Hero
        title="Master New Skills, Share Your Expertise"
        subtitle="Join thousands of learners and teachers in our vibrant skill-sharing community"
        backgroundImage={images.hero}
        ctaText="Get Started Today"
        ctaLink="/register"
      />

      {/* Services Section */}
      <Section title="What We Offer" subtitle="Discover the power of collaborative learning">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card key={index} className="text-center p-8 card-hover">
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <FontAwesomeIcon icon={service.icon} className="text-2xl text-white" />
              </div>
              <h3 className="text-xl font-bold text-dark mb-4">{service.title}</h3>
              <p className="text-text-light">{service.description}</p>
            </Card>
          ))}
        </div>
      </Section>

      {/* About Section */}
      <Section className="bg-gray-50">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <img 
              src={images.about} 
              alt="About SkillSwap" 
              className="rounded-lg shadow-medium w-full"
            />
          </div>
          <div>
            <h2 className="text-3xl font-bold text-dark mb-6">
              Why Choose SkillSwap?
            </h2>
            <ul className="space-y-4 mb-8">
              {features.map((feature, index) => (
                <li key={index} className="flex items-center">
                  <FontAwesomeIcon icon={faArrowRight} className="text-primary mr-3" />
                  <span className="text-text">{feature}</span>
                </li>
              ))}
            </ul>
            <Button variant="primary" size="lg">
              Learn More About Us
            </Button>
          </div>
        </div>
      </Section>

      {/* Stats Section */}
      <Section title="Our Impact" subtitle="Numbers that speak for themselves">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="bg-white p-6 rounded-lg shadow-soft card-hover">
                <h3 className="text-3xl font-bold text-primary mb-2">{stat.number}</h3>
                <p className="text-text-light">{stat.title}</p>
              </div>
            </div>
          ))}
        </div>
      </Section>

      {/* How It Works */}
      <Section title="How It Works" subtitle="Get started in just a few simple steps" className="bg-gray-50">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <div key={index} className="text-center">
              <div className="w-12 h-12 bg-primary text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                {step.number}
              </div>
              <h3 className="text-lg font-semibold text-dark mb-2">{step.title}</h3>
              <p className="text-text-light text-sm">{step.description}</p>
            </div>
          ))}
        </div>
      </Section>

      {/* Trending Skills */}
      <Section title="Trending Skills" subtitle="Most popular skills in our community">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {trendingSkills.map((skill, index) => (
            <Card key={index} className="p-6 card-hover">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-dark">{skill.name}</h3>
                <span className="text-primary font-bold">{skill.count}</span>
              </div>
              <p className="text-text-light text-sm mb-4">{skill.description}</p>
              <Button variant="outline" size="sm" className="w-full">
                Learn More
              </Button>
            </Card>
          ))}
        </div>
      </Section>

      {/* Testimonials */}
      <Section title="What Our Users Say" subtitle="Real stories from our community" className="bg-gray-50">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="p-6">
              <p className="text-text italic mb-4">"{testimonial.text}"</p>
              <div className="border-t border-gray-200 pt-4">
                <div className="font-semibold text-dark">{testimonial.author}</div>
                <div className="text-text-light text-sm">{testimonial.role}</div>
              </div>
            </Card>
          ))}
        </div>
      </Section>

      {/* CTA Section */}
      <Section className="bg-primary text-white text-center">
        <div className="max-w-2xl mx-auto">
          <h2 className="text-3xl font-bold mb-4">Ready to Start Your Learning Journey?</h2>
          <p className="text-lg mb-8 opacity-90">
            Join thousands of learners and teachers in our vibrant community today.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="secondary" size="lg" className="bg-white text-primary hover:bg-gray-100">
              Start Learning
            </Button>
            <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-primary">
              Become a Teacher
            </Button>
          </div>
        </div>
      </Section>
    </div>
  );
};

export default Home;
