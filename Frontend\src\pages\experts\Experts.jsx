import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSearch,
  faStar,
  faMapMarkerAlt,
  faGraduationCap,
  faUsers,
  faComment,
  faHeart
} from '@fortawesome/free-solid-svg-icons';

const Experts = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSkill, setSelectedSkill] = useState('all');

  const experts = [
    {
      id: 1,
      name: '<PERSON>',
      avatar: 'https://via.placeholder.com/100',
      title: 'Senior React Developer',
      location: 'San Francisco, CA',
      rating: 4.9,
      reviews: 156,
      students: 1250,
      skills: ['React', 'JavaScript', 'Node.js'],
      hourlyRate: '$75',
      bio: 'Experienced full-stack developer with 8+ years in web development.'
    },
    {
      id: 2,
      name: '<PERSON>',
      avatar: 'https://via.placeholder.com/100',
      title: 'Data Science Expert',
      location: 'New York, NY',
      rating: 4.8,
      reviews: 203,
      students: 890,
      skills: ['Python', 'Machine Learning', 'Data Analysis'],
      hourlyRate: '$85',
      bio: 'PhD in Computer Science, specializing in AI and machine learning.'
    },
    {
      id: 3,
      name: 'Emily Rodriguez',
      avatar: 'https://via.placeholder.com/100',
      title: 'UX/UI Design Specialist',
      location: 'Austin, TX',
      rating: 4.9,
      reviews: 178,
      students: 650,
      skills: ['Figma', 'Adobe XD', 'User Research'],
      hourlyRate: '$65',
      bio: 'Creative designer with a passion for user-centered design.'
    }
  ];

  const skills = ['all', 'React', 'Python', 'JavaScript', 'Design', 'Data Science', 'Node.js'];

  const filteredExperts = experts.filter(expert => {
    const matchesSearch = expert.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         expert.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         expert.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesSkill = selectedSkill === 'all' || 
                        expert.skills.some(skill => skill.toLowerCase().includes(selectedSkill.toLowerCase()));
    
    return matchesSearch && matchesSkill;
  });

  return (
    <div className="bg-background min-h-screen py-8">
      <div className="container mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-dark mb-4">Find Expert Teachers</h1>
          <p className="text-lg text-text max-w-2xl mx-auto">
            Connect with skilled professionals who are passionate about sharing their knowledge.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-soft p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search by name, skill, or expertise..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                />
                <FontAwesomeIcon 
                  icon={faSearch} 
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"
                />
              </div>
            </div>

            {/* Skill Filter */}
            <select
              value={selectedSkill}
              onChange={(e) => setSelectedSkill(e.target.value)}
              className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
            >
              {skills.map(skill => (
                <option key={skill} value={skill}>
                  {skill === 'all' ? 'All Skills' : skill}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Experts Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredExperts.map(expert => (
            <div key={expert.id} className="bg-white rounded-lg shadow-soft p-6 card-hover">
              {/* Expert Header */}
              <div className="flex items-center gap-4 mb-4">
                <img
                  src={expert.avatar}
                  alt={expert.name}
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div className="flex-1">
                  <h3 className="text-lg font-bold text-dark">{expert.name}</h3>
                  <p className="text-text-light text-sm">{expert.title}</p>
                  <div className="flex items-center gap-1 text-text-light text-sm">
                    <FontAwesomeIcon icon={faMapMarkerAlt} />
                    {expert.location}
                  </div>
                </div>
                <button className="text-gray-400 hover:text-red-500 transition-colors">
                  <FontAwesomeIcon icon={faHeart} />
                </button>
              </div>

              {/* Rating and Stats */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-1">
                    <FontAwesomeIcon icon={faStar} className="text-yellow-400" />
                    <span className="font-medium">{expert.rating}</span>
                  </div>
                  <span className="text-text-light text-sm">({expert.reviews} reviews)</span>
                </div>
                <div className="text-primary font-bold">{expert.hourlyRate}/hr</div>
              </div>

              {/* Bio */}
              <p className="text-text text-sm mb-4">{expert.bio}</p>

              {/* Skills */}
              <div className="mb-4">
                <div className="flex flex-wrap gap-2">
                  {expert.skills.map((skill, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-xs font-medium"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>

              {/* Stats */}
              <div className="flex items-center justify-between text-sm text-text-light mb-4">
                <div className="flex items-center gap-1">
                  <FontAwesomeIcon icon={faUsers} />
                  {expert.students} students
                </div>
                <div className="flex items-center gap-1">
                  <FontAwesomeIcon icon={faComment} />
                  {expert.reviews} reviews
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                <button className="btn-primary flex-1">
                  Contact
                </button>
                <button className="btn-outline flex-1">
                  View Profile
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredExperts.length === 0 && (
          <div className="text-center py-12">
            <FontAwesomeIcon icon={faGraduationCap} className="text-6xl text-gray-300 mb-4" />
            <h3 className="text-xl font-medium text-gray-500 mb-2">No experts found</h3>
            <p className="text-gray-400">Try adjusting your search criteria</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Experts;
