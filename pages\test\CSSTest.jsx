import React from 'react';

const CSSTest = () => {
  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="container mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8 text-primary">
          CSS Test Page
        </h1>
        
        {/* Test Tailwind Classes */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-2xl font-semibold mb-4 text-gray-800">
            Tailwind CSS Classes Test
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-500 text-white p-4 rounded">
              Blue Background
            </div>
            <div className="bg-green-500 text-white p-4 rounded">
              Green Background
            </div>
            <div className="bg-red-500 text-white p-4 rounded">
              Red Background
            </div>
          </div>
        </div>

        {/* Test Custom CSS Classes */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-2xl font-semibold mb-4 text-gray-800">
            Custom CSS Classes Test
          </h2>
          <div className="space-y-4">
            <button className="btn btn-primary mr-4">
              Primary Button
            </button>
            <button className="btn btn-secondary mr-4">
              Secondary Button
            </button>
            <button className="btn btn-outline">
              Outline Button
            </button>
          </div>
        </div>

        {/* Test Cards */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-2xl font-semibold mb-4 text-gray-800">
            Card Components Test
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="card p-6">
              <h3 className="text-lg font-semibold mb-2">Regular Card</h3>
              <p className="text-gray-600">This is a regular card component.</p>
            </div>
            <div className="card card-hover p-6">
              <h3 className="text-lg font-semibold mb-2">Hover Card</h3>
              <p className="text-gray-600">This card has hover effects.</p>
            </div>
          </div>
        </div>

        {/* Test Forms */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-semibold mb-4 text-gray-800">
            Form Elements Test
          </h2>
          <div className="space-y-4">
            <div className="form-group">
              <label className="form-label">Test Input</label>
              <input 
                type="text" 
                className="form-input" 
                placeholder="Enter some text..."
              />
            </div>
            <div className="form-group">
              <label className="form-label">Test Textarea</label>
              <textarea 
                className="form-input" 
                rows="3"
                placeholder="Enter some text..."
              ></textarea>
            </div>
          </div>
        </div>

        {/* Test Responsive Design */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            Resize your browser window to test responsive design
          </p>
        </div>
      </div>
    </div>
  );
};

export default CSSTest;
