import { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useAuth } from '../../../context/AuthContext';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faBell,
  faCalendarAlt,
  faUsers,
  faChalkboardTeacher,
  faGraduationCap,
  faStar,
  faCheckCircle,
  faTimesCircle,
  faEye,
  faChevronRight,
  faComments,
  faBookOpen,
  faClock,
  faTrophy
} from '@fortawesome/free-solid-svg-icons';

import Button from '../../../components/ui/Button';

const Dashboard = () => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      type: 'skill_request',
      title: 'New skill exchange request',
      message: '<PERSON> wants to exchange Python for your JavaScript skills',
      time: '2 hours ago',
      avatar: 'https://via.placeholder.com/40',
      unread: true
    },
    {
      id: 2,
      type: 'meeting',
      title: 'Upcoming session reminder',
      message: 'React fundamentals session starts in 30 minutes',
      time: '30 minutes',
      avatar: 'https://via.placeholder.com/40',
      unread: true
    },
    {
      id: 3,
      type: 'feedback',
      title: 'New feedback received',
      message: 'John rated your JavaScript course 5 stars',
      time: '1 day ago',
      avatar: 'https://via.placeholder.com/40',
      unread: false
    }
  ]);

  const [upcomingMeetings] = useState([
    {
      id: 1,
      title: 'React Fundamentals',
      instructor: 'Mike Johnson',
      time: '2:00 PM',
      date: '15',
      month: 'Apr',
      type: 'learning'
    },
    {
      id: 2,
      title: 'Python Basics',
      student: 'Sarah Wilson',
      time: '4:00 PM',
      date: '16',
      month: 'Apr',
      type: 'teaching'
    }
  ]);

  const [recentFeedback] = useState([
    {
      id: 1,
      user: 'John Doe',
      avatar: 'https://via.placeholder.com/40',
      rating: 5,
      comment: 'Excellent JavaScript course! Very clear explanations.',
      date: '2 days ago',
      course: 'JavaScript Fundamentals'
    },
    {
      id: 2,
      user: 'Emma Smith',
      avatar: 'https://via.placeholder.com/40',
      rating: 4,
      comment: 'Great React tutorial, learned a lot!',
      date: '3 days ago',
      course: 'React Basics'
    }
  ]);

  const stats = [
    {
      title: 'Skills Learned',
      value: '12',
      change: '+2 this month',
      icon: faGraduationCap,
      color: 'bg-blue-500'
    },
    {
      title: 'Skills Taught',
      value: '8',
      change: '+1 this month',
      icon: faChalkboardTeacher,
      color: 'bg-green-500'
    },
    {
      title: 'Active Exchanges',
      value: '5',
      change: '+3 this week',
      icon: faUsers,
      color: 'bg-purple-500'
    },
    {
      title: 'Average Rating',
      value: '4.8',
      change: '+0.2 this month',
      icon: faStar,
      color: 'bg-yellow-500'
    }
  ];

  const handleNotificationAction = (notificationId, action) => {
    setNotifications(notifications.map(notif => 
      notif.id === notificationId 
        ? { ...notif, unread: false }
        : notif
    ));
    
    if (action === 'accept') {
      alert('Skill exchange request accepted!');
    } else if (action === 'decline') {
      alert('Skill exchange request declined.');
    }
  };

  return (
    <div className="bg-background min-h-screen p-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-dark mb-2">
          Welcome back, {user?.name || 'User'}!
        </h1>
        <p className="text-lg text-text-light">
          Here's what's happening with your learning journey today.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg shadow-soft p-6 card-hover">
            <div className={`w-12 h-12 ${stat.color} rounded-xl flex items-center justify-center mb-4`}>
              <FontAwesomeIcon icon={stat.icon} className="text-white text-xl" />
            </div>
            <div className="text-2xl font-bold text-dark mb-1">{stat.value}</div>
            <div className="text-sm text-text-light mb-2">{stat.title}</div>
            <div className="text-xs text-success">{stat.change}</div>
          </div>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Notifications and Meetings */}
        <div className="lg:col-span-2 space-y-8">
          {/* Notifications */}
          <div className="bg-white rounded-lg shadow-soft p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-dark flex items-center gap-2">
                <FontAwesomeIcon icon={faBell} className="text-primary" />
                Recent Notifications
              </h2>
              <Link to="/notifications" className="text-primary hover:text-primary-dark text-sm">
                View All
              </Link>
            </div>
            
            <div className="space-y-4">
              {notifications.slice(0, 3).map((notification) => (
                <div key={notification.id} className={`flex items-start gap-4 p-4 rounded-lg border ${
                  notification.unread ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'
                }`}>
                  <img 
                    src={notification.avatar} 
                    alt="User" 
                    className="w-10 h-10 rounded-full"
                  />
                  <div className="flex-1">
                    <div className="font-medium text-dark">{notification.title}</div>
                    <div className="text-sm text-text-light">{notification.message}</div>
                    <div className="text-xs text-text-muted mt-1">{notification.time}</div>
                  </div>
                  {notification.type === 'skill_request' && (
                    <div className="flex gap-2">
                      <button 
                        onClick={() => handleNotificationAction(notification.id, 'accept')}
                        className="p-2 text-success hover:bg-success hover:text-white rounded transition-all"
                        title="Accept"
                      >
                        <FontAwesomeIcon icon={faCheckCircle} />
                      </button>
                      <button 
                        onClick={() => handleNotificationAction(notification.id, 'decline')}
                        className="p-2 text-error hover:bg-error hover:text-white rounded transition-all"
                        title="Decline"
                      >
                        <FontAwesomeIcon icon={faTimesCircle} />
                      </button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Upcoming Meetings */}
          <div className="bg-white rounded-lg shadow-soft p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-dark flex items-center gap-2">
                <FontAwesomeIcon icon={faCalendarAlt} className="text-primary" />
                Upcoming Sessions
              </h2>
              <Link to="/schedule" className="text-primary hover:text-primary-dark text-sm">
                View Schedule
              </Link>
            </div>
            
            <div className="space-y-4">
              {upcomingMeetings.map((meeting) => (
                <div key={meeting.id} className="flex items-center gap-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">{meeting.date}</div>
                    <div className="text-xs text-text-light uppercase">{meeting.month}</div>
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-dark">{meeting.title}</div>
                    <div className="text-sm text-text-light">
                      {meeting.type === 'learning' ? `with ${meeting.instructor}` : `teaching ${meeting.student}`}
                    </div>
                    <div className="text-xs text-text-muted flex items-center gap-1 mt-1">
                      <FontAwesomeIcon icon={faClock} />
                      {meeting.time}
                    </div>
                  </div>
                  <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                    meeting.type === 'learning' 
                      ? 'bg-blue-100 text-blue-700' 
                      : 'bg-green-100 text-green-700'
                  }`}>
                    {meeting.type}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column - Quick Actions and Recent Feedback */}
        <div className="space-y-8">
          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-soft p-6">
            <h2 className="text-xl font-bold text-dark mb-6">Quick Actions</h2>
            <div className="space-y-3">
              <Link to="/discover" className="btn-primary w-full flex items-center justify-center gap-2">
                <FontAwesomeIcon icon={faBookOpen} />
                Find New Skills
              </Link>
              <Link to="/profile" className="btn-outline w-full flex items-center justify-center gap-2">
                <FontAwesomeIcon icon={faUsers} />
                Update Profile
              </Link>
              <Link to="/messages" className="btn-ghost w-full flex items-center justify-center gap-2">
                <FontAwesomeIcon icon={faComments} />
                Messages
              </Link>
            </div>
          </div>

          {/* Recent Feedback */}
          <div className="bg-white rounded-lg shadow-soft p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-dark flex items-center gap-2">
                <FontAwesomeIcon icon={faTrophy} className="text-primary" />
                Recent Feedback
              </h2>
            </div>
            
            <div className="space-y-4">
              {recentFeedback.map((feedback) => (
                <div key={feedback.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <img 
                      src={feedback.avatar} 
                      alt={feedback.user} 
                      className="w-8 h-8 rounded-full"
                    />
                    <div className="flex-1">
                      <div className="font-medium text-dark text-sm">{feedback.user}</div>
                      <div className="text-xs text-text-light">{feedback.date}</div>
                    </div>
                    <div className="flex items-center gap-1">
                      {[...Array(5)].map((_, i) => (
                        <FontAwesomeIcon 
                          key={i}
                          icon={faStar} 
                          className={i < feedback.rating ? 'text-yellow-400' : 'text-gray-300'}
                          size="sm"
                        />
                      ))}
                    </div>
                  </div>
                  <p className="text-sm text-text mb-2">"{feedback.comment}"</p>
                  <div className="text-xs text-text-muted">Course: {feedback.course}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
