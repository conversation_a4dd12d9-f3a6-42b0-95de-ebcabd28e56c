import { useState } from 'react';
import { useAuth } from '../../../context/AuthContext';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUser,
  faEnvelope,
  faPhone,
  faMapMarkerAlt,
  faEdit,
  faStar,
  faGraduationCap,
  faChalkboardTeacher,
  faCalendarAlt,
  faTrophy
} from '@fortawesome/free-solid-svg-icons';

const Profile = () => {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState({
    name: user?.name || '<PERSON>',
    email: user?.email || '<EMAIL>',
    phone: '+****************',
    location: 'San Francisco, CA',
    bio: 'Passionate developer and teacher with 5+ years of experience in web development.',
    skills: ['JavaScript', 'React', 'Node.js', 'Python', 'MongoDB'],
    rating: 4.8,
    totalStudents: 156,
    coursesCompleted: 12,
    hoursTeaching: 240
  });

  const handleSave = () => {
    setIsEditing(false);
    // Here you would typically save to backend
    alert('Profile updated successfully!');
  };

  const stats = [
    { icon: faGraduationCap, label: 'Courses Completed', value: profileData.coursesCompleted },
    { icon: faChalkboardTeacher, label: 'Students Taught', value: profileData.totalStudents },
    { icon: faClock, label: 'Teaching Hours', value: profileData.hoursTeaching },
    { icon: faTrophy, label: 'Average Rating', value: `${profileData.rating}/5` }
  ];

  return (
    <div className="bg-background min-h-screen p-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-soft p-8 mb-8">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-3xl font-bold text-dark">My Profile</h1>
            <button
              onClick={() => setIsEditing(!isEditing)}
              className="btn-primary flex items-center gap-2"
            >
              <FontAwesomeIcon icon={faEdit} />
              {isEditing ? 'Cancel' : 'Edit Profile'}
            </button>
          </div>

          <div className="flex flex-col md:flex-row gap-8">
            {/* Profile Image */}
            <div className="flex-shrink-0">
              <div className="w-32 h-32 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                {user?.profileImage ? (
                  <img 
                    src={user.profileImage} 
                    alt="Profile" 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <FontAwesomeIcon icon={faUser} className="text-gray-400 text-4xl" />
                )}
              </div>
            </div>

            {/* Profile Info */}
            <div className="flex-1">
              {isEditing ? (
                <div className="space-y-4">
                  <input
                    type="text"
                    value={profileData.name}
                    onChange={(e) => setProfileData({...profileData, name: e.target.value})}
                    className="form-input text-2xl font-bold"
                  />
                  <input
                    type="email"
                    value={profileData.email}
                    onChange={(e) => setProfileData({...profileData, email: e.target.value})}
                    className="form-input"
                  />
                  <input
                    type="tel"
                    value={profileData.phone}
                    onChange={(e) => setProfileData({...profileData, phone: e.target.value})}
                    className="form-input"
                  />
                  <input
                    type="text"
                    value={profileData.location}
                    onChange={(e) => setProfileData({...profileData, location: e.target.value})}
                    className="form-input"
                  />
                  <textarea
                    value={profileData.bio}
                    onChange={(e) => setProfileData({...profileData, bio: e.target.value})}
                    rows={3}
                    className="form-input"
                  />
                  <button onClick={handleSave} className="btn-primary">
                    Save Changes
                  </button>
                </div>
              ) : (
                <div>
                  <h2 className="text-2xl font-bold text-dark mb-2">{profileData.name}</h2>
                  <div className="space-y-2 text-text-light mb-4">
                    <div className="flex items-center gap-2">
                      <FontAwesomeIcon icon={faEnvelope} />
                      {profileData.email}
                    </div>
                    <div className="flex items-center gap-2">
                      <FontAwesomeIcon icon={faPhone} />
                      {profileData.phone}
                    </div>
                    <div className="flex items-center gap-2">
                      <FontAwesomeIcon icon={faMapMarkerAlt} />
                      {profileData.location}
                    </div>
                  </div>
                  <p className="text-text">{profileData.bio}</p>
                  
                  {/* Rating */}
                  <div className="flex items-center gap-2 mt-4">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <FontAwesomeIcon 
                          key={i}
                          icon={faStar} 
                          className={i < Math.floor(profileData.rating) ? 'text-yellow-400' : 'text-gray-300'}
                        />
                      ))}
                    </div>
                    <span className="text-text-light">({profileData.rating}/5)</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div key={index} className="bg-white rounded-lg shadow-soft p-6 text-center">
              <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center mx-auto mb-4">
                <FontAwesomeIcon icon={stat.icon} className="text-white text-xl" />
              </div>
              <div className="text-2xl font-bold text-dark mb-1">{stat.value}</div>
              <div className="text-sm text-text-light">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* Skills */}
        <div className="bg-white rounded-lg shadow-soft p-8">
          <h3 className="text-xl font-bold text-dark mb-6">Skills</h3>
          <div className="flex flex-wrap gap-3">
            {profileData.skills.map((skill, index) => (
              <span
                key={index}
                className="px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium"
              >
                {skill}
              </span>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
