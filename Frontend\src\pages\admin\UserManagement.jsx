import { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCheckCircle,
  faTimesCircle,
  faBan,
  faUndo,
  faFlag,
  faExclamationTriangle,
  faSearch,
  faFilter,
  faEye,
  faComment,
} from "@fortawesome/free-solid-svg-icons";

// Sample data
const sampleUsers = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "https://via.placeholder.com/40",
    status: "active",
    joinDate: "2023-01-15",
    skills: ["JavaScript", "React"],
    reportCount: 0,
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "https://via.placeholder.com/40",
    status: "pending",
    joinDate: "2023-02-20",
    skills: ["Python", "Django"],
    reportCount: 1,
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "https://via.placeholder.com/40",
    status: "suspended",
    joinDate: "2023-03-10",
    skills: ["Java", "Spring"],
    reportCount: 3,
  },
];

const sampleReports = [
  {
    id: 1,
    userId: 2,
    type: "Inappropriate Content",
    content: "User posted inappropriate content in their profile.",
    date: "2023-04-15",
    status: "pending",
  },
  {
    id: 2,
    userId: 3,
    type: "Spam",
    content: "User is sending spam messages to other users.",
    date: "2023-04-18",
    status: "pending",
  },
];

const UserManagement = () => {
  const [users, setUsers] = useState(sampleUsers);
  const [reports, setReports] = useState(sampleReports);
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [showReportModal, setShowReportModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [responseText, setResponseText] = useState("");

  const handleStatusChange = (userId, newStatus) => {
    setUsers(
      users.map((user) =>
        user.id === userId ? { ...user, status: newStatus } : user
      )
    );
  };

  const handleViewReports = (user) => {
    setSelectedUser(user);
    setShowReportModal(true);
  };

  const handleCloseModal = () => {
    setShowReportModal(false);
    setSelectedUser(null);
    setResponseText("");
  };

  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === "all" || user.status === statusFilter;
    const matchesTab =
      activeTab === "all" ||
      (activeTab === "pending" && user.status === "pending") ||
      (activeTab === "active" && user.status === "active") ||
      (activeTab === "suspended" && user.status === "suspended");

    return matchesSearch && matchesStatus && matchesTab;
  });

  const getStatusBadgeClasses = (status) => {
    switch (status) {
      case "active":
        return "bg-success-light text-success-dark";
      case "pending":
        return "bg-warning-light text-warning-dark";
      case "suspended":
        return "bg-error-light text-error-dark";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  const getActionButtonClasses = (action) => {
    switch (action) {
      case "approve":
        return "text-success hover:bg-success hover:text-white";
      case "suspend":
        return "text-error hover:bg-error hover:text-white";
      case "view":
        return "text-primary hover:bg-primary hover:text-white";
      default:
        return "text-gray-600 hover:bg-gray-600 hover:text-white";
    }
  };

  return (
    <div className="space-y-6">
      {/* Filter Bar */}
      <div className="flex justify-between items-center gap-4 mb-6">
        <div className="flex items-center bg-gray-50 rounded-lg px-4 py-2 flex-1 max-w-md border border-border">
          <FontAwesomeIcon icon={faSearch} className="text-gray-400 mr-3" />
          <input
            type="text"
            placeholder="Search users..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="bg-transparent border-none outline-none w-full text-text"
          />
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-4 py-2 rounded-lg border border-border bg-white text-text cursor-pointer"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="pending">Pending</option>
          <option value="suspended">Suspended</option>
        </select>
      </div>

      {/* Tabs */}
      <div className="flex gap-4 mb-6 border-b border-border">
        {["all", "active", "pending", "suspended"].map((tab) => (
          <button
            key={tab}
            className={`px-4 py-2 border-b-3 transition-all duration-300 capitalize ${
              activeTab === tab
                ? "text-primary border-primary font-bold"
                : "text-text border-transparent hover:text-primary"
            }`}
            onClick={() => setActiveTab(tab)}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg shadow-soft overflow-hidden">
        <table className="w-full">
          <thead className="bg-gray-50 border-b-2 border-border">
            <tr>
              <th className="px-4 py-3 text-left font-semibold text-text">User</th>
              <th className="px-4 py-3 text-left font-semibold text-text">Status</th>
              <th className="px-4 py-3 text-left font-semibold text-text">Join Date</th>
              <th className="px-4 py-3 text-left font-semibold text-text">Skills</th>
              <th className="px-4 py-3 text-left font-semibold text-text">Reports</th>
              <th className="px-4 py-3 text-left font-semibold text-text">Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredUsers.map((user) => (
              <tr
                key={user.id}
                className="border-b border-border hover:bg-gray-50 transition-colors"
              >
                <td className="px-4 py-3">
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
                      <img
                        src={user.avatar}
                        alt={user.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <div className="font-semibold text-dark">{user.name}</div>
                      <div className="text-sm text-text-light">{user.email}</div>
                    </div>
                  </div>
                </td>
                <td className="px-4 py-3">
                  <span
                    className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${getStatusBadgeClasses(
                      user.status
                    )}`}
                  >
                    {user.status}
                  </span>
                </td>
                <td className="px-4 py-3 text-text">{user.joinDate}</td>
                <td className="px-4 py-3">
                  <div className="flex flex-wrap gap-1">
                    {user.skills.map((skill, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-primary-100 text-primary-700 rounded text-xs"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </td>
                <td className="px-4 py-3">
                  {user.reportCount > 0 ? (
                    <button
                      onClick={() => handleViewReports(user)}
                      className="flex items-center gap-1 text-error hover:text-error-dark"
                    >
                      <FontAwesomeIcon icon={faFlag} />
                      {user.reportCount}
                    </button>
                  ) : (
                    <span className="text-gray-400">None</span>
                  )}
                </td>
                <td className="px-4 py-3">
                  <div className="flex gap-2">
                    {user.status === "pending" && (
                      <button
                        onClick={() => handleStatusChange(user.id, "active")}
                        className={`p-2 rounded transition-all duration-300 ${getActionButtonClasses(
                          "approve"
                        )}`}
                        title="Approve User"
                      >
                        <FontAwesomeIcon icon={faCheckCircle} />
                      </button>
                    )}
                    {user.status === "active" && (
                      <button
                        onClick={() => handleStatusChange(user.id, "suspended")}
                        className={`p-2 rounded transition-all duration-300 ${getActionButtonClasses(
                          "suspend"
                        )}`}
                        title="Suspend User"
                      >
                        <FontAwesomeIcon icon={faBan} />
                      </button>
                    )}
                    {user.status === "suspended" && (
                      <button
                        onClick={() => handleStatusChange(user.id, "active")}
                        className={`p-2 rounded transition-all duration-300 ${getActionButtonClasses(
                          "approve"
                        )}`}
                        title="Reactivate User"
                      >
                        <FontAwesomeIcon icon={faUndo} />
                      </button>
                    )}
                    <button
                      onClick={() => handleViewReports(user)}
                      className={`p-2 rounded transition-all duration-300 ${getActionButtonClasses(
                        "view"
                      )}`}
                      title="View Details"
                    >
                      <FontAwesomeIcon icon={faEye} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Report Modal */}
      {showReportModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-100">
          <div className="bg-white rounded-xl p-6 w-full max-w-2xl shadow-strong">
            <div className="flex justify-between items-center mb-6 pb-4 border-b border-border">
              <h3 className="text-xl font-bold text-dark flex items-center gap-2">
                <FontAwesomeIcon icon={faFlag} />
                Reports for {selectedUser.name}
              </h3>
              <button
                onClick={handleCloseModal}
                className="text-text-light hover:text-dark text-xl"
              >
                ×
              </button>
            </div>
            
            <div className="space-y-4 mb-6">
              {reports
                .filter((report) => report.userId === selectedUser.id)
                .map((report) => (
                  <div
                    key={report.id}
                    className="p-4 border border-border rounded-lg"
                  >
                    <div className="flex justify-between mb-2">
                      <div className="font-semibold text-dark">{report.type}</div>
                      <div className="text-sm text-text-light">{report.date}</div>
                    </div>
                    <p className="text-text mb-4">{report.content}</p>
                    <button
                      onClick={() => handleResolveReport(report.id)}
                      className="btn-primary btn-sm"
                    >
                      Mark as Resolved
                    </button>
                  </div>
                ))}
            </div>

            <div className="border-t border-border pt-4">
              <h4 className="font-medium text-dark mb-2">Send Response</h4>
              <textarea
                value={responseText}
                onChange={(e) => setResponseText(e.target.value)}
                placeholder="Type your response to the user..."
                className="w-full p-3 border border-border rounded-lg resize-vertical min-h-24 mb-4"
              />
              <div className="flex justify-end gap-3">
                <button onClick={handleCloseModal} className="btn-ghost">
                  Cancel
                </button>
                <button
                  onClick={() => {
                    alert(`Response sent to ${selectedUser.name}`);
                    handleCloseModal();
                  }}
                  className="btn-primary"
                >
                  Send Response
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagement;
