import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUsers,
  faGraduationCap,
  faHandshake,
  faGlobe,
  faLightbulb
} from '@fortawesome/free-solid-svg-icons';

const About = () => {
  const values = [
    {
      icon: faUsers,
      title: "Community First",
      description: "We believe in the power of community-driven learning and knowledge sharing."
    },
    {
      icon: faGraduationCap,
      title: "Quality Education",
      description: "We're committed to providing high-quality, accessible education for everyone."
    },
    {
      icon: faHandshake,
      title: "Mutual Growth",
      description: "Learning is a two-way street. We grow together through teaching and learning."
    },
    {
      icon: faGlobe,
      title: "Global Reach",
      description: "Connecting learners and teachers from around the world, breaking down barriers."
    }
  ];

  return (
    <div className="bg-background min-h-screen">
      {/* Hero Section */}
      <div className="bg-primary text-white py-20">
        <div className="container mx-auto text-center">
          <h1 className="text-5xl font-bold mb-6">About SkillSwap</h1>
          <p className="text-xl max-w-3xl mx-auto">
            We're on a mission to democratize education by connecting passionate learners 
            with expert teachers in a collaborative, community-driven platform.
          </p>
        </div>
      </div>

      {/* Mission Section */}
      <div className="py-20">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-dark mb-6">Our Mission</h2>
              <p className="text-lg text-text mb-6">
                At SkillSwap, we believe that everyone has something valuable to teach and 
                something important to learn. Our platform breaks down traditional barriers 
                to education, creating a space where knowledge flows freely between individuals.
              </p>
              <p className="text-lg text-text">
                Whether you're looking to master a new skill, share your expertise, or 
                exchange knowledge with peers, SkillSwap provides the tools and community 
                to make meaningful learning connections.
              </p>
            </div>
            <div className="bg-white rounded-lg shadow-soft p-8">
              <div className="text-center">
                <FontAwesomeIcon icon={faLightbulb} className="text-6xl text-primary mb-4" />
                <h3 className="text-2xl font-bold text-dark mb-4">Learn. Teach. Grow.</h3>
                <p className="text-text">
                  Join thousands of learners and teachers who are transforming their lives 
                  through skill sharing and collaborative learning.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Values Section */}
      <div className="bg-gray-50 py-20">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-dark mb-6">Our Values</h2>
            <p className="text-lg text-text max-w-2xl mx-auto">
              These core values guide everything we do and shape the SkillSwap community.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="bg-white rounded-lg shadow-soft p-8 text-center card-hover">
                <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
                  <FontAwesomeIcon icon={value.icon} className="text-2xl text-white" />
                </div>
                <h3 className="text-xl font-bold text-dark mb-4">{value.title}</h3>
                <p className="text-text">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-20">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl font-bold text-dark mb-6">Ready to Join Our Community?</h2>
          <p className="text-lg text-text mb-8 max-w-2xl mx-auto">
            Start your learning journey today and become part of a global community 
            of passionate learners and teachers.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="btn-primary btn-lg">
              Get Started
            </button>
            <button className="btn-outline btn-lg">
              Learn More
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default About;
