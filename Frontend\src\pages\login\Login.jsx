import { useState, useEffect } from "react";
import { useNavigate, Link } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUser, faLock, faSignInAlt } from "@fortawesome/free-solid-svg-icons";

const Login = () => {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const { login, user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (user) {
      navigate("/home");
    }
  }, [user, navigate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (error) setError("");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
      // Basic validation
      if (!formData.email || !formData.password) {
        setError("Please fill in all fields");
        return;
      }

      if (!formData.email.includes("@")) {
        setError("Please enter a valid email address");
        return;
      }

      // Simulate login process
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Mock login logic - in real app, this would call an API
      if (
        formData.email === "<EMAIL>" &&
        formData.password === "admin123"
      ) {
        login({
          id: 1,
          name: "Admin User",
          email: formData.email,
          role: "admin",
        });
        navigate("/admin");
      } else if (
        formData.email === "<EMAIL>" &&
        formData.password === "user123"
      ) {
        login({
          id: 2,
          name: "Regular User",
          email: formData.email,
          role: "user",
        });
        navigate("/home");
      } else {
        setError("Invalid email or password");
      }
    } catch (err) {
      setError("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleAdminLogin = () => {
    setFormData({
      email: "<EMAIL>",
      password: "admin123",
    });
  };

  return (
    <div className="bg-gray-900 min-h-screen flex flex-col items-center justify-center p-8">
      <h1 className="text-white text-4xl font-bold mb-8 text-center">
        Welcome to SkillSwap
      </h1>

      <div className="bg-white rounded-lg w-full max-w-md shadow-strong p-8">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Email Field */}
          <div className="form-group">
            <label htmlFor="email" className="form-label">
              Email Address
            </label>
            <div className="relative">
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className="form-input pl-10"
                placeholder="Enter your email"
                required
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FontAwesomeIcon icon={faUser} className="text-gray-400" />
              </div>
            </div>
          </div>

          {/* Password Field */}
          <div className="form-group">
            <label htmlFor="password" className="form-label">
              Password
            </label>
            <div className="relative">
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                className="form-input pl-10"
                placeholder="Enter your password"
                required
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FontAwesomeIcon icon={faLock} className="text-gray-400" />
              </div>
            </div>
          </div>

          {/* Error Message */}
          {error && <div className="form-error">{error}</div>}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            className={`btn-primary w-full flex items-center justify-center gap-2 ${
              isLoading ? "opacity-50 cursor-not-allowed" : ""
            }`}
          >
            {isLoading ? (
              <>
                <div className="loading-spinner w-4 h-4"></div>
                Signing In...
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faSignInAlt} />
                Sign In
              </>
            )}
          </button>

          {/* Admin Login Button */}
          <button
            type="button"
            onClick={handleAdminLogin}
            className="btn-secondary w-full"
          >
            Quick Admin Login
          </button>
        </form>

        {/* Links */}
        <div className="mt-6 text-center space-y-3">
          <Link
            to="/create-account"
            className="block text-primary hover:text-primary-dark transition-colors"
          >
            Create an account
          </Link>
          <Link
            to="/"
            className="block text-primary hover:text-primary-dark transition-colors"
          >
            Forgot password?
          </Link>
        </div>

        {/* Demo Credentials */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-sm font-medium text-gray-700 mb-2">
            Demo Credentials:
          </h3>
          <div className="text-xs text-gray-600 space-y-1">
            <div>
              <strong>Admin:</strong> <EMAIL> / admin123
            </div>
            <div>
              <strong>User:</strong> <EMAIL> / user123
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-8 text-center text-gray-400 text-sm">
        <p>&copy; 2024 SkillSwap. All rights reserved.</p>
      </div>
    </div>
  );
};

export default Login;
