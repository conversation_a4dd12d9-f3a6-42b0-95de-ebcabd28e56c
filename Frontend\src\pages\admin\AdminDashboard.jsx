import { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faUsers,
  faListAlt,
  faCertificate,
  faUserShield,
  faCheckCircle,
  faTimesCircle,
  faBan,
  faEdit,
  faTrash,
  faEye,
  faFlag,
  faExclamationTriangle,
} from "@fortawesome/free-solid-svg-icons";

import Button from "../../components/ui/Button";
import UserManagement from "./UserManagement";
import SkillModeration from "./SkillModeration";

const AdminDashboard = () => {
  const [activeTab, setActiveTab] = useState("users");

  return (
    <div className="bg-background min-h-screen p-8">
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-dark mb-2">Admin Dashboard</h1>
        <p className="text-lg text-text-light mb-6">
          Manage users, skill listings, and platform content
        </p>
      </div>

      <div className="flex gap-4 mb-8 border-b border-border">
        <button
          className={`px-6 py-4 flex items-center gap-2 border-b-3 transition-all duration-300 ${
            activeTab === "users"
              ? "bg-white text-primary border-primary font-bold"
              : "bg-transparent text-text border-transparent hover:bg-gray-100 hover:text-primary"
          }`}
          onClick={() => setActiveTab("users")}
        >
          <FontAwesomeIcon icon={faUsers} />
          User Management
        </button>
        <button
          className={`px-6 py-4 flex items-center gap-2 border-b-3 transition-all duration-300 ${
            activeTab === "skills"
              ? "bg-white text-primary border-primary font-bold"
              : "bg-transparent text-text border-transparent hover:bg-gray-100 hover:text-primary"
          }`}
          onClick={() => setActiveTab("skills")}
        >
          <FontAwesomeIcon icon={faListAlt} />
          Skill Listings
        </button>
      </div>

      <div className="bg-white rounded-xl p-8 shadow-soft">
        {activeTab === "users" && <UserManagement />}
        {activeTab === "skills" && <SkillModeration />}
      </div>
    </div>
  );
};

export default AdminDashboard;
