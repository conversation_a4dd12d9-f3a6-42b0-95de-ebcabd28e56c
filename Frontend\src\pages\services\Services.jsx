import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faGraduationCap,
  faChalkboardTeacher,
  faHandshake,
  faUsers,
  faVideo,
  faCertificate,
  faComments,
  faCalendarAlt,
  faLaptopCode,
  faPalette,
  faChartLine,
  faLanguage,
  faMusic,
  faCamera
} from '@fortawesome/free-solid-svg-icons';

const Services = () => {
  const mainServices = [
    {
      icon: faGraduationCap,
      title: "Learn New Skills",
      description: "Access thousands of courses taught by expert instructors from around the world.",
      features: ["Expert-led courses", "Interactive content", "Progress tracking", "Certificates"]
    },
    {
      icon: faChalkboardTeacher,
      title: "Teach & Earn",
      description: "Share your expertise and earn money by teaching others what you know best.",
      features: ["Create courses", "Set your rates", "Build your audience", "Flexible schedule"]
    },
    {
      icon: faHandshake,
      title: "Skill Exchange",
      description: "Trade skills directly with other users in our community-driven platform.",
      features: ["Direct skill trading", "Community matching", "Mutual learning", "No money involved"]
    }
  ];

  const additionalServices = [
    {
      icon: faVideo,
      title: "Live Sessions",
      description: "Real-time learning with interactive video sessions"
    },
    {
      icon: faCertificate,
      title: "Certifications",
      description: "Earn verified certificates upon course completion"
    },
    {
      icon: faComments,
      title: "Community Support",
      description: "Get help from our vibrant learning community"
    },
    {
      icon: faCalendarAlt,
      title: "Flexible Scheduling",
      description: "Learn at your own pace with flexible timing"
    }
  ];

  const categories = [
    { icon: faLaptopCode, name: "Programming", courses: "500+" },
    { icon: faPalette, name: "Design", courses: "300+" },
    { icon: faChartLine, name: "Business", courses: "250+" },
    { icon: faLanguage, name: "Languages", courses: "200+" },
    { icon: faMusic, name: "Music", courses: "150+" },
    { icon: faCamera, name: "Photography", courses: "100+" }
  ];

  const pricingPlans = [
    {
      name: "Free",
      price: "$0",
      period: "forever",
      features: [
        "Access to free courses",
        "Basic skill exchange",
        "Community access",
        "Limited messaging"
      ],
      popular: false
    },
    {
      name: "Pro",
      price: "$19",
      period: "per month",
      features: [
        "All free features",
        "Unlimited course access",
        "Priority support",
        "Advanced analytics",
        "Unlimited messaging"
      ],
      popular: true
    },
    {
      name: "Teacher",
      price: "$39",
      period: "per month",
      features: [
        "All Pro features",
        "Create unlimited courses",
        "Advanced teaching tools",
        "Revenue analytics",
        "Marketing support"
      ],
      popular: false
    }
  ];

  return (
    <div className="bg-background min-h-screen">
      {/* Hero Section */}
      <div className="bg-primary text-white py-20">
        <div className="container mx-auto text-center">
          <h1 className="text-5xl font-bold mb-6">Our Services</h1>
          <p className="text-xl max-w-3xl mx-auto">
            Comprehensive learning solutions designed to help you grow your skills 
            and advance your career through our innovative platform.
          </p>
        </div>
      </div>

      {/* Main Services */}
      <div className="py-20">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-dark mb-6">What We Offer</h2>
            <p className="text-lg text-text max-w-2xl mx-auto">
              Choose from our range of services designed to meet your learning and teaching needs.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {mainServices.map((service, index) => (
              <div key={index} className="bg-white rounded-lg shadow-soft p-8 card-hover">
                <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6">
                  <FontAwesomeIcon icon={service.icon} className="text-2xl text-white" />
                </div>
                <h3 className="text-2xl font-bold text-dark mb-4 text-center">{service.title}</h3>
                <p className="text-text mb-6 text-center">{service.description}</p>
                
                <ul className="space-y-2">
                  {service.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-text">
                      <div className="w-2 h-2 bg-primary rounded-full mr-3"></div>
                      {feature}
                    </li>
                  ))}
                </ul>
                
                <button className="btn-primary w-full mt-6">
                  Get Started
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Additional Services */}
      <div className="bg-gray-50 py-20">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-dark mb-6">Additional Features</h2>
            <p className="text-lg text-text max-w-2xl mx-auto">
              Enhanced features to make your learning experience even better.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {additionalServices.map((service, index) => (
              <div key={index} className="bg-white rounded-lg shadow-soft p-6 text-center card-hover">
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FontAwesomeIcon icon={service.icon} className="text-xl text-primary" />
                </div>
                <h3 className="text-lg font-bold text-dark mb-3">{service.title}</h3>
                <p className="text-text text-sm">{service.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Categories */}
      <div className="py-20">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-dark mb-6">Popular Categories</h2>
            <p className="text-lg text-text max-w-2xl mx-auto">
              Explore our most popular skill categories with thousands of courses.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {categories.map((category, index) => (
              <div key={index} className="bg-white rounded-lg shadow-soft p-6 text-center card-hover">
                <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                  <FontAwesomeIcon icon={category.icon} className="text-xl text-white" />
                </div>
                <h3 className="font-bold text-dark mb-2">{category.name}</h3>
                <p className="text-primary text-sm font-medium">{category.courses}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Pricing */}
      <div className="bg-gray-50 py-20">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-dark mb-6">Choose Your Plan</h2>
            <p className="text-lg text-text max-w-2xl mx-auto">
              Select the plan that best fits your learning or teaching goals.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {pricingPlans.map((plan, index) => (
              <div key={index} className={`bg-white rounded-lg shadow-soft p-8 relative ${
                plan.popular ? 'ring-2 ring-primary transform scale-105' : ''
              }`}>
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-primary text-white px-4 py-2 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-dark mb-4">{plan.name}</h3>
                  <div className="mb-4">
                    <span className="text-4xl font-bold text-primary">{plan.price}</span>
                    <span className="text-text-light">/{plan.period}</span>
                  </div>
                </div>

                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-text">
                      <div className="w-2 h-2 bg-success rounded-full mr-3"></div>
                      {feature}
                    </li>
                  ))}
                </ul>

                <button className={`w-full py-3 rounded-lg font-medium transition-colors ${
                  plan.popular 
                    ? 'bg-primary text-white hover:bg-primary-dark' 
                    : 'border border-primary text-primary hover:bg-primary hover:text-white'
                }`}>
                  Get Started
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-primary text-white py-20">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl font-bold mb-6">Ready to Start Learning?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Join thousands of learners and teachers who are already transforming their lives.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="btn-secondary btn-lg bg-white text-primary hover:bg-gray-100">
              Start Learning
            </button>
            <button className="btn-outline btn-lg border-white text-white hover:bg-white hover:text-primary">
              Become a Teacher
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Services;
