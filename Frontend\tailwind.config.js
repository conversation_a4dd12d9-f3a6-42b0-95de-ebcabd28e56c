/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Custom SkillSwap color scheme
        primary: {
          DEFAULT: '#7e57c2',  // Main purple
          dark: '#5e35b1',     // Darker purple for hover effects
          light: '#9575cd',    // Lighter purple
          50: '#f3f0ff',
          100: '#e9e5ff',
          200: '#d6ccff',
          300: '#b8a3ff',
          400: '#9575cd',
          500: '#7e57c2',
          600: '#5e35b1',
          700: '#4a2c85',
          800: '#3d2369',
          900: '#2d1b4e',
        },
        secondary: {
          DEFAULT: '#9575cd',  // Lighter purple
          dark: '#7e57c2',     // Darker secondary for hover effects
          light: '#b39ddb',    // Even lighter purple
        },
        accent: {
          DEFAULT: '#e83d6a',  // Pink accent color
          dark: '#c2185b',
          light: '#f06292',
        },
        background: '#f8f9fa', // Light gray background
        dark: '#2d3748',       // Dark text/elements
        light: '#ffffff',      // White
        text: {
          DEFAULT: '#333333',  // Main text color
          light: '#666666',    // Light text color
          muted: '#9ca3af',    // Muted text
        },
        border: {
          DEFAULT: '#e2e8f0',  // Border color
          light: '#f1f5f9',    // Light border
          dark: '#cbd5e1',     // Dark border
        },
        success: {
          DEFAULT: '#48bb78',  // Green for success states
          dark: '#38a169',
          light: '#68d391',
        },
        warning: {
          DEFAULT: '#ed8936',  // Orange for warning states
          dark: '#dd6b20',
          light: '#f6ad55',
        },
        error: {
          DEFAULT: '#e53e3e',  // Red for error states
          dark: '#c53030',
          light: '#fc8181',
        },

        // Keep some Tailwind defaults for flexibility
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        }
      },
      fontSize: {
        'xs': '0.75rem',     // 12px
        'sm': '0.875rem',    // 14px
        'md': '1rem',        // 16px (base)
        'lg': '1.125rem',    // 18px
        'xl': '1.25rem',     // 20px
        '2xl': '1.5rem',     // 24px
        '3xl': '1.875rem',   // 30px
        '4xl': '2.25rem',    // 36px
        '5xl': '3rem',       // 48px
        '6xl': '5rem',       // 80px
      },
      spacing: {
        'xs': '0.25rem',     // 4px
        'sm': '0.5rem',      // 8px
        'md': '1rem',        // 16px
        'lg': '1.5rem',      // 24px
        'xl': '2rem',        // 32px
        '2xl': '3rem',       // 48px
        '3xl': '4rem',       // 64px
      },
      borderRadius: {
        'sm': '0.25rem',     // 4px
        'md': '0.5rem',      // 8px
        'lg': '1rem',        // 16px
        'full': '9999px',    // Fully rounded
      },
      fontFamily: {
        sans: ['Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Open Sans', 'Helvetica Neue', 'sans-serif'],
      },
      boxShadow: {
        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
        'medium': '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        'card': '0 4px 6px rgba(0, 0, 0, 0.05)',
        'card-hover': '0 10px 20px rgba(0, 0, 0, 0.1)',
        'button': '0 4px 8px rgba(0, 0, 0, 0.1)',
      },
      maxWidth: {
        'container': '1200px',
      },
      lineHeight: {
        'tight': '1.2',
        'normal': '1.6',
      },
      transitionProperty: {
        'all': 'all',
      },
      transitionDuration: {
        '300': '300ms',
        '400': '400ms',
        '600': '600ms',
      },
      transitionTimingFunction: {
        'ease': 'ease',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'fade-out': 'fadeOut 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'slide-left': 'slideLeft 0.3s ease-out',
        'slide-right': 'slideRight 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'bounce-soft': 'bounceSoft 0.6s ease-in-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeOut: {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideLeft: {
          '0%': { transform: 'translateX(10px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        slideRight: {
          '0%': { transform: 'translateX(-10px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        bounceSoft: {
          '0%, 20%, 53%, 80%, 100%': { transform: 'translate3d(0,0,0)' },
          '40%, 43%': { transform: 'translate3d(0, -5px, 0)' },
          '70%': { transform: 'translate3d(0, -3px, 0)' },
          '90%': { transform: 'translate3d(0, -1px, 0)' },
        },
      },
      zIndex: {
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100',
      },
    },
  },
  plugins: [],
}
