@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base layer customizations */
@layer base {
  * {
    @apply m-0 p-0 box-border;
  }

  body {
    @apply font-sans bg-background text-text leading-normal;
    line-height: 1.6;
  }

  a {
    @apply no-underline text-primary transition-colors duration-300 ease;
  }

  a:hover {
    @apply text-accent;
  }

  button {
    @apply cursor-pointer font-sans;
  }

  img {
    @apply max-w-full h-auto;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold leading-tight mb-4;
  }

  h1 {
    @apply text-4xl;
  }

  h2 {
    @apply text-3xl;
  }

  h3 {
    @apply text-2xl;
  }

  h4 {
    @apply text-xl;
  }

  h5 {
    @apply text-lg;
  }

  h6 {
    @apply text-base;
  }

  p {
    @apply mb-4;
  }

  ul, ol {
    @apply mb-4 pl-6;
  }

  section {
    @apply py-16;
  }

  /* Form elements */
  input, textarea, select {
    @apply border border-border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent;
  }

  input:focus, textarea:focus, select:focus {
    @apply ring-2 ring-primary border-transparent;
  }

  /* Scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
}

/* Component layer for reusable patterns */
@layer components {
  .container {
    @apply w-full max-w-container mx-auto px-6;
  }

  .Logo {
    @apply w-10 h-10 rounded-full;
  }

  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply btn bg-primary text-white hover:bg-primary-dark focus:ring-primary;
  }

  .btn-secondary {
    @apply btn bg-secondary text-white hover:bg-secondary-dark focus:ring-secondary;
  }

  .btn-accent {
    @apply btn bg-accent text-white hover:bg-accent-dark focus:ring-accent;
  }

  .btn-outline {
    @apply btn border border-primary text-primary hover:bg-primary hover:text-white focus:ring-primary;
  }

  .btn-ghost {
    @apply btn text-primary hover:bg-primary hover:bg-opacity-10 focus:ring-primary;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }

  .btn-lg {
    @apply px-6 py-3 text-lg;
  }

  /* Card variants */
  .card {
    @apply bg-white rounded-lg shadow-soft border border-gray-200;
  }

  .card-hover {
    @apply card transition-shadow duration-300 hover:shadow-medium;
  }

  .card-interactive {
    @apply card-hover cursor-pointer transform transition-transform duration-300 hover:scale-105;
  }

  /* Status badges */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-success {
    @apply badge bg-success-light text-success-dark;
  }

  .badge-warning {
    @apply badge bg-warning-light text-warning-dark;
  }

  .badge-error {
    @apply badge bg-error-light text-error-dark;
  }

  .badge-primary {
    @apply badge bg-primary-100 text-primary-700;
  }

  /* Form groups */
  .form-group {
    @apply mb-4;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent;
  }

  .form-error {
    @apply text-error text-sm mt-1;
  }

  /* Loading states */
  .loading {
    @apply animate-pulse;
  }

  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary;
  }

  /* Utility classes */
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent;
  }

  .shadow-glow {
    box-shadow: 0 0 20px rgba(126, 87, 194, 0.3);
  }

  .backdrop-blur {
    backdrop-filter: blur(10px);
  }
}

/* Utility layer for custom utilities */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}