@tailwind base;
@tailwind components;
@tailwind utilities;

/* Basic reset and styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #f8fafc;
  color: #334155;
  line-height: 1.6;
}

/* Component styles using standard CSS */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  text-decoration: none;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

.btn-outline {
  border: 1px solid #3b82f6;
  color: #3b82f6;
  background-color: transparent;
}

.btn-outline:hover {
  background-color: #3b82f6;
  color: white;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.125rem;
}

.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.card-hover {
  transition: box-shadow 0.3s ease;
}

.card-hover:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.form-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* Component layer for reusable patterns */
@layer components {
  .container {
    @apply w-full max-w-container mx-auto px-6;
  }

  .Logo {
    @apply w-10 h-10 rounded-full;
  }

  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply btn bg-primary text-white hover:bg-primary-dark focus:ring-primary;
  }

  .btn-secondary {
    @apply btn bg-secondary text-white hover:bg-secondary-dark focus:ring-secondary;
  }

  .btn-accent {
    @apply btn bg-accent text-white hover:bg-accent-dark focus:ring-accent;
  }

  .btn-outline {
    @apply btn border border-primary text-primary hover:bg-primary hover:text-white focus:ring-primary;
  }

  .btn-ghost {
    @apply btn text-primary hover:bg-primary hover:bg-opacity-10 focus:ring-primary;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }

  .btn-lg {
    @apply px-6 py-3 text-lg;
  }

  /* Card variants */
  .card {
    @apply bg-white rounded-lg shadow-soft border border-gray-200;
  }

  .card-hover {
    @apply card transition-shadow duration-300 hover:shadow-medium;
  }

  .card-interactive {
    @apply card-hover cursor-pointer transform transition-transform duration-300 hover:scale-105;
  }

  /* Status badges */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-success {
    @apply badge bg-success-light text-success-dark;
  }

  .badge-warning {
    @apply badge bg-warning-light text-warning-dark;
  }

  .badge-error {
    @apply badge bg-error-light text-error-dark;
  }

  .badge-primary {
    @apply badge bg-primary-100 text-primary-700;
  }

  /* Form groups */
  .form-group {
    @apply mb-4;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent;
  }

  .form-error {
    @apply text-error text-sm mt-1;
  }

  /* Loading states */
  .loading {
    @apply animate-pulse;
  }

  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary;
  }

  /* Utility classes */
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent;
  }

  .shadow-glow {
    box-shadow: 0 0 20px rgba(126, 87, 194, 0.3);
  }

  .backdrop-blur {
    backdrop-filter: blur(10px);
  }
}

/* Utility layer for custom utilities */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}