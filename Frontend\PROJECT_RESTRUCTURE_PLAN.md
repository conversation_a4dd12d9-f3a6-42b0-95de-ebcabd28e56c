# SkillSwap Frontend Restructure Plan

## 🎯 Objective
Convert entire project from styled-components to Tailwind CSS and reorganize into industry standard folder structure.

## 📁 New Industry Standard Structure

```
Frontend/src/
├── app/                          # App configuration and providers
│   ├── App.jsx                   # Main App component
│   ├── store/                    # State management (if needed)
│   └── providers/                # Context providers
├── components/                   # Reusable UI components
│   ├── ui/                       # Basic UI components
│   │   ├── Button/
│   │   │   ├── Button.jsx
│   │   │   ├── Button.test.jsx
│   │   │   └── index.js
│   │   ├── Card/
│   │   ├── Input/
│   │   ├── Modal/
│   │   └── index.js
│   ├── layout/                   # Layout components
│   │   ├── Header/
│   │   ├── Footer/
│   │   ├── Sidebar/
│   │   └── index.js
│   ├── forms/                    # Form components
│   │   ├── LoginForm/
│   │   ├── RegisterForm/
│   │   └── index.js
│   └── common/                   # Common components
│       ├── LoadingSpinner/
│       ├── ErrorBoundary/
│       └── index.js
├── features/                     # Feature-based modules
│   ├── auth/                     # Authentication feature
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── services/
│   │   ├── types/
│   │   └── index.js
│   ├── admin/                    # Admin feature
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   ├── utils/
│   │   └── index.js
│   ├── dashboard/                # Dashboard feature
│   ├── profile/                  # Profile feature
│   ├── messaging/                # Messaging feature
│   ├── scheduling/               # Scheduling feature
│   └── skills/                   # Skills management feature
├── pages/                        # Page components (route handlers)
│   ├── HomePage/
│   ├── LoginPage/
│   ├── DashboardPage/
│   ├── AdminPage/
│   └── index.js
├── hooks/                        # Custom React hooks
│   ├── useAuth.js
│   ├── useLocalStorage.js
│   └── index.js
├── services/                     # API and external services
│   ├── api/
│   │   ├── auth.js
│   │   ├── users.js
│   │   └── index.js
│   ├── storage/
│   └── index.js
├── utils/                        # Utility functions
│   ├── constants.js
│   ├── helpers.js
│   ├── formatters.js
│   └── index.js
├── types/                        # TypeScript types (if using TS)
├── assets/                       # Static assets
│   ├── images/
│   ├── icons/
│   └── fonts/
├── styles/                       # Global styles and Tailwind config
│   ├── globals.css
│   ├── components.css
│   └── utilities.css
├── lib/                          # Third-party library configurations
├── __tests__/                    # Global test utilities
└── main.jsx                      # Entry point
```

## 🔄 Migration Steps

### Phase 1: Setup New Structure
1. Create new folder structure
2. Update Tailwind configuration
3. Remove styled-components dependencies

### Phase 2: Convert Global Styles
1. Replace GlobalStyles.js with Tailwind base styles
2. Update theme.js to Tailwind config
3. Remove ThemeProvider from App.jsx

### Phase 3: Convert Components by Feature
1. Admin module (partially done)
2. Authentication components
3. Dashboard components
4. Profile components
5. Messaging components
6. UI components

### Phase 4: Convert Pages
1. Home page
2. Login/Register pages
3. Dashboard page
4. Admin pages
5. Profile pages
6. Other pages

### Phase 5: Final Cleanup
1. Remove styled-components imports
2. Update all import paths
3. Remove unused files
4. Update documentation

## 🎨 Tailwind CSS Conversion Guidelines

### Color Mapping
- `var(--primary-color)` → `text-primary` or `bg-primary`
- `var(--secondary-color)` → `text-secondary` or `bg-secondary`
- `var(--accent-color)` → `text-accent` or `bg-accent`
- `var(--background-color)` → `bg-background`
- `var(--text-color)` → `text-text`
- `var(--border-color)` → `border-border`

### Spacing Mapping
- `var(--spacing-xs)` → `p-1` or `m-1`
- `var(--spacing-sm)` → `p-2` or `m-2`
- `var(--spacing-md)` → `p-4` or `m-4`
- `var(--spacing-lg)` → `p-6` or `m-6`
- `var(--spacing-xl)` → `p-8` or `m-8`
- `var(--spacing-2xl)` → `p-12` or `m-12`
- `var(--spacing-3xl)` → `p-16` or `m-16`

### Font Size Mapping
- `var(--font-size-xs)` → `text-xs`
- `var(--font-size-sm)` → `text-sm`
- `var(--font-size-md)` → `text-base`
- `var(--font-size-lg)` → `text-lg`
- `var(--font-size-xl)` → `text-xl`
- `var(--font-size-2xl)` → `text-2xl`
- `var(--font-size-3xl)` → `text-3xl`
- `var(--font-size-4xl)` → `text-4xl`
- `var(--font-size-5xl)` → `text-5xl`
- `var(--font-size-6xl)` → `text-6xl`

## 📋 Files to Convert (47 total)

### Admin Module (3 files)
- [ ] AdminDashboard.jsx
- [ ] UserManagement.jsx  
- [ ] SkillModeration.jsx

### Main Pages (14 files)
- [ ] Home.jsx
- [ ] Dashboard.jsx
- [ ] Login.jsx
- [ ] Register.jsx
- [ ] Profile.jsx
- [ ] About.jsx
- [ ] Contact.jsx
- [ ] CreateAccount.jsx
- [ ] Discover.jsx
- [ ] Experts.jsx
- [ ] Messages.jsx
- [ ] Schedule.jsx
- [ ] Services.jsx
- [ ] Settings.jsx

### Global Files (2 files)
- [ ] App.jsx (remove ThemeProvider)
- [ ] GlobalStyles.js (replace with Tailwind)

## 🚀 Benefits Expected

1. **Performance**: Smaller bundle size without styled-components
2. **Development Speed**: Faster styling with Tailwind utilities
3. **Consistency**: Unified design system
4. **Maintainability**: Better organized code structure
5. **Scalability**: Feature-based architecture
6. **Developer Experience**: Better IntelliSense and tooling
