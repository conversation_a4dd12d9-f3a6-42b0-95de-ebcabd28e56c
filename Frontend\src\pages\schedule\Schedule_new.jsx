import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCalendarAlt,
  faPlus,
  faClock,
  faUser,
  faVideo,
  faMapMarkerAlt,
  faChevronLeft,
  faChevronRight
} from '@fortawesome/free-solid-svg-icons';

const Schedule = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showAddModal, setShowAddModal] = useState(false);

  const sessions = [
    {
      id: 1,
      title: 'React Fundamentals',
      instructor: '<PERSON>',
      student: 'You',
      date: '2024-04-15',
      time: '2:00 PM',
      duration: '1 hour',
      type: 'learning',
      location: 'Online',
      status: 'confirmed'
    },
    {
      id: 2,
      title: 'Python Basics',
      instructor: 'You',
      student: '<PERSON>',
      date: '2024-04-16',
      time: '4:00 PM',
      duration: '1.5 hours',
      type: 'teaching',
      location: 'Online',
      status: 'pending'
    }
  ];

  const getDaysInMonth = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }
    
    return days;
  };

  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const getSessionsForDate = (date) => {
    const dateString = date.toISOString().split('T')[0];
    return sessions.filter(session => session.date === dateString);
  };

  const navigateMonth = (direction) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(currentDate.getMonth() + direction);
    setCurrentDate(newDate);
  };

  return (
    <div className="bg-background min-h-screen p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold text-dark mb-2">My Schedule</h1>
            <p className="text-lg text-text-light">
              Manage your learning sessions and teaching appointments
            </p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="btn-primary flex items-center gap-2"
          >
            <FontAwesomeIcon icon={faPlus} />
            Schedule Session
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Calendar */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-soft p-6">
              {/* Calendar Header */}
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-dark">
                  {currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                </h2>
                <div className="flex gap-2">
                  <button
                    onClick={() => navigateMonth(-1)}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded"
                  >
                    <FontAwesomeIcon icon={faChevronLeft} />
                  </button>
                  <button
                    onClick={() => navigateMonth(1)}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded"
                  >
                    <FontAwesomeIcon icon={faChevronRight} />
                  </button>
                </div>
              </div>

              {/* Calendar Grid */}
              <div className="grid grid-cols-7 gap-1 mb-4">
                {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                  <div key={day} className="p-2 text-center font-medium text-gray-600">
                    {day}
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-7 gap-1">
                {getDaysInMonth(currentDate).map((day, index) => (
                  <div key={index} className="aspect-square">
                    {day && (
                      <button
                        onClick={() => setSelectedDate(day)}
                        className={`w-full h-full p-2 text-center rounded transition-colors ${
                          day.toDateString() === selectedDate.toDateString()
                            ? 'bg-primary text-white'
                            : day.toDateString() === new Date().toDateString()
                            ? 'bg-primary-100 text-primary'
                            : 'hover:bg-gray-100'
                        }`}
                      >
                        <div className="text-sm">{day.getDate()}</div>
                        {getSessionsForDate(day).length > 0 && (
                          <div className="w-2 h-2 bg-accent rounded-full mx-auto mt-1"></div>
                        )}
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sessions for Selected Date */}
          <div>
            <div className="bg-white rounded-lg shadow-soft p-6">
              <h3 className="text-xl font-bold text-dark mb-4">
                {formatDate(selectedDate)}
              </h3>
              
              <div className="space-y-4">
                {getSessionsForDate(selectedDate).length > 0 ? (
                  getSessionsForDate(selectedDate).map(session => (
                    <div key={session.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-dark">{session.title}</h4>
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          session.type === 'learning' 
                            ? 'bg-blue-100 text-blue-700' 
                            : 'bg-green-100 text-green-700'
                        }`}>
                          {session.type}
                        </span>
                      </div>
                      
                      <div className="space-y-1 text-sm text-text-light">
                        <div className="flex items-center gap-2">
                          <FontAwesomeIcon icon={faClock} />
                          {session.time} ({session.duration})
                        </div>
                        <div className="flex items-center gap-2">
                          <FontAwesomeIcon icon={faUser} />
                          {session.type === 'learning' ? `with ${session.instructor}` : `teaching ${session.student}`}
                        </div>
                        <div className="flex items-center gap-2">
                          <FontAwesomeIcon icon={session.location === 'Online' ? faVideo : faMapMarkerAlt} />
                          {session.location}
                        </div>
                      </div>
                      
                      <div className="flex gap-2 mt-3">
                        <button className="btn-primary btn-sm flex-1">
                          Join
                        </button>
                        <button className="btn-outline btn-sm">
                          Reschedule
                        </button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <FontAwesomeIcon icon={faCalendarAlt} className="text-4xl text-gray-300 mb-3" />
                    <p className="text-gray-500">No sessions scheduled for this date</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Schedule;
