import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSearch,
  faFilter,
  faStar,
  faUsers,
  faClock,
  faGraduationCap,
  faCode,
  faPalette,
  faChartLine
} from '@fortawesome/free-solid-svg-icons';

const Discover = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedLevel, setSelectedLevel] = useState('all');

  const categories = [
    { id: 'all', name: 'All Categories', icon: faGraduationCap },
    { id: 'programming', name: 'Programming', icon: faCode },
    { id: 'design', name: 'Design', icon: faPalette },
    { id: 'business', name: 'Business', icon: faChartLine }
  ];

  const skills = [
    {
      id: 1,
      title: 'React Development Masterclass',
      instructor: '<PERSON>',
      category: 'programming',
      level: 'intermediate',
      rating: 4.8,
      students: 1250,
      duration: '12 hours',
      price: 'Free',
      image: 'https://via.placeholder.com/300x200'
    },
    {
      id: 2,
      title: 'UI/UX Design Fundamentals',
      instructor: 'Jane <PERSON>',
      category: 'design',
      level: 'beginner',
      rating: 4.9,
      students: 890,
      duration: '8 hours',
      price: 'Exchange',
      image: 'https://via.placeholder.com/300x200'
    },
    {
      id: 3,
      title: 'Digital Marketing Strategy',
      instructor: 'Mike Johnson',
      category: 'business',
      level: 'advanced',
      rating: 4.7,
      students: 650,
      duration: '15 hours',
      price: 'Free',
      image: 'https://via.placeholder.com/300x200'
    }
  ];

  const filteredSkills = skills.filter(skill => {
    const matchesSearch = skill.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         skill.instructor.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || skill.category === selectedCategory;
    const matchesLevel = selectedLevel === 'all' || skill.level === selectedLevel;
    
    return matchesSearch && matchesCategory && matchesLevel;
  });

  return (
    <div className="bg-background min-h-screen py-8">
      <div className="container mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-dark mb-4">Discover Skills</h1>
          <p className="text-lg text-text max-w-2xl mx-auto">
            Explore thousands of skills taught by expert instructors from around the world.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-soft p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search for skills, instructors, or topics..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                />
                <FontAwesomeIcon 
                  icon={faSearch} 
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"
                />
              </div>
            </div>

            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
            >
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>

            {/* Level Filter */}
            <select
              value={selectedLevel}
              onChange={(e) => setSelectedLevel(e.target.value)}
              className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <option value="all">All Levels</option>
              <option value="beginner">Beginner</option>
              <option value="intermediate">Intermediate</option>
              <option value="advanced">Advanced</option>
            </select>
          </div>
        </div>

        {/* Categories */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          {categories.map(category => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`p-4 rounded-lg border-2 transition-all ${
                selectedCategory === category.id
                  ? 'border-primary bg-primary-50 text-primary'
                  : 'border-gray-200 bg-white text-gray-600 hover:border-primary hover:text-primary'
              }`}
            >
              <FontAwesomeIcon icon={category.icon} className="text-2xl mb-2" />
              <div className="font-medium">{category.name}</div>
            </button>
          ))}
        </div>

        {/* Skills Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredSkills.map(skill => (
            <div key={skill.id} className="bg-white rounded-lg shadow-soft overflow-hidden card-hover">
              <img 
                src={skill.image} 
                alt={skill.title}
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    skill.level === 'beginner' ? 'bg-green-100 text-green-700' :
                    skill.level === 'intermediate' ? 'bg-yellow-100 text-yellow-700' :
                    'bg-red-100 text-red-700'
                  }`}>
                    {skill.level}
                  </span>
                  <span className="text-primary font-bold">{skill.price}</span>
                </div>
                
                <h3 className="text-lg font-bold text-dark mb-2">{skill.title}</h3>
                <p className="text-text-light text-sm mb-4">by {skill.instructor}</p>
                
                <div className="flex items-center justify-between text-sm text-text-light">
                  <div className="flex items-center gap-1">
                    <FontAwesomeIcon icon={faStar} className="text-yellow-400" />
                    {skill.rating}
                  </div>
                  <div className="flex items-center gap-1">
                    <FontAwesomeIcon icon={faUsers} />
                    {skill.students}
                  </div>
                  <div className="flex items-center gap-1">
                    <FontAwesomeIcon icon={faClock} />
                    {skill.duration}
                  </div>
                </div>
                
                <button className="btn-primary w-full mt-4">
                  Learn More
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredSkills.length === 0 && (
          <div className="text-center py-12">
            <FontAwesomeIcon icon={faGraduationCap} className="text-6xl text-gray-300 mb-4" />
            <h3 className="text-xl font-medium text-gray-500 mb-2">No skills found</h3>
            <p className="text-gray-400">Try adjusting your search or filter criteria</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Discover;
