import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCheckCircle,
  faTimesCircle,
  faBan,
  faUndo,
  faFlag,
  faExclamationTriangle,
  faSearch,
  faFilter,
  faEye,
  faComment,
  faGraduationCap
} from '@fortawesome/free-solid-svg-icons';

// Sample data
const sampleSkills = [
  {
    id: 1,
    name: "React Development",
    category: "Programming",
    description: "Learn modern React development with hooks and context",
    instructor: "<PERSON>",
    status: "approved",
    submittedDate: "2023-04-10",
    reportCount: 0,
    rating: 4.8,
    enrollments: 156
  },
  {
    id: 2,
    name: "Python for Beginners",
    category: "Programming",
    description: "Complete Python course for absolute beginners",
    instructor: "<PERSON>",
    status: "pending",
    submittedDate: "2023-04-15",
    reportCount: 0,
    rating: 0,
    enrollments: 0
  },
  {
    id: 3,
    name: "Digital Marketing",
    category: "Marketing",
    description: "Learn digital marketing strategies and tools",
    instructor: "<PERSON>",
    status: "flagged",
    submittedDate: "2023-04-12",
    reportCount: 2,
    rating: 3.2,
    enrollments: 45
  },
  {
    id: 4,
    name: "Graphic Design Basics",
    category: "Design",
    description: "Introduction to graphic design principles",
    instructor: "<PERSON> <PERSON>",
    status: "rejected",
    submittedDate: "2023-04-08",
    reportCount: 1,
    rating: 0,
    enrollments: 0
  }
];

const SkillModeration = () => {
  const [skills, setSkills] = useState(sampleSkills);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");

  const handleStatusChange = (skillId, newStatus) => {
    setSkills(skills.map(skill => 
      skill.id === skillId ? { ...skill, status: newStatus } : skill
    ));
  };

  const filteredSkills = skills.filter(skill => {
    const matchesSearch = 
      skill.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      skill.instructor.toLowerCase().includes(searchQuery.toLowerCase()) ||
      skill.category.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || skill.status === statusFilter;
    const matchesCategory = categoryFilter === "all" || skill.category === categoryFilter;
    
    return matchesSearch && matchesStatus && matchesCategory;
  });

  const getStatusBadgeClasses = (status) => {
    switch (status) {
      case "approved":
        return "bg-success-light text-success-dark";
      case "pending":
        return "bg-warning-light text-warning-dark";
      case "flagged":
        return "bg-error-light text-error-dark";
      case "rejected":
        return "bg-gray-100 text-gray-600";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  const getActionButtonClasses = (action) => {
    switch (action) {
      case "approve":
        return "text-success hover:bg-success hover:text-white";
      case "reject":
        return "text-error hover:bg-error hover:text-white";
      case "flag":
        return "text-warning hover:bg-warning hover:text-white";
      case "view":
        return "text-primary hover:bg-primary hover:text-white";
      default:
        return "text-gray-600 hover:bg-gray-600 hover:text-white";
    }
  };

  const categories = ["all", "Programming", "Design", "Marketing", "Business", "Language"];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2 mb-6">
        <FontAwesomeIcon icon={faGraduationCap} className="text-primary" />
        <h2 className="text-2xl font-bold text-dark">Skill Moderation</h2>
      </div>

      {/* Filter Bar */}
      <div className="flex justify-between items-center gap-4 mb-6">
        <div className="flex items-center bg-gray-50 rounded-lg px-4 py-2 flex-1 max-w-md border border-border">
          <FontAwesomeIcon icon={faSearch} className="text-gray-400 mr-3" />
          <input
            type="text"
            placeholder="Search skills..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="bg-transparent border-none outline-none w-full text-text"
          />
        </div>
        
        <div className="flex gap-3">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 rounded-lg border border-border bg-white text-text cursor-pointer"
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="flagged">Flagged</option>
            <option value="rejected">Rejected</option>
          </select>
          
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="px-4 py-2 rounded-lg border border-border bg-white text-text cursor-pointer"
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category === "all" ? "All Categories" : category}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Skills Grid */}
      <div className="grid gap-6">
        {filteredSkills.map((skill) => (
          <div key={skill.id} className="bg-white rounded-lg shadow-soft border border-gray-200 p-6">
            <div className="flex justify-between items-start mb-4">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h3 className="text-xl font-semibold text-dark">{skill.name}</h3>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusBadgeClasses(skill.status)}`}>
                    {skill.status}
                  </span>
                  {skill.reportCount > 0 && (
                    <span className="flex items-center gap-1 text-error text-sm">
                      <FontAwesomeIcon icon={faFlag} />
                      {skill.reportCount} reports
                    </span>
                  )}
                </div>
                
                <div className="text-sm text-text-light mb-2">
                  <span className="font-medium">Category:</span> {skill.category} | 
                  <span className="font-medium ml-2">Instructor:</span> {skill.instructor} |
                  <span className="font-medium ml-2">Submitted:</span> {skill.submittedDate}
                </div>
                
                <p className="text-text mb-3">{skill.description}</p>
                
                {skill.status === "approved" && (
                  <div className="flex items-center gap-4 text-sm text-text-light">
                    <span>⭐ {skill.rating}/5</span>
                    <span>👥 {skill.enrollments} enrolled</span>
                  </div>
                )}
              </div>
              
              <div className="flex gap-2 ml-4">
                {skill.status === "pending" && (
                  <>
                    <button
                      onClick={() => handleStatusChange(skill.id, "approved")}
                      className={`p-2 rounded transition-all duration-300 ${getActionButtonClasses("approve")}`}
                      title="Approve Skill"
                    >
                      <FontAwesomeIcon icon={faCheckCircle} />
                    </button>
                    <button
                      onClick={() => handleStatusChange(skill.id, "rejected")}
                      className={`p-2 rounded transition-all duration-300 ${getActionButtonClasses("reject")}`}
                      title="Reject Skill"
                    >
                      <FontAwesomeIcon icon={faTimesCircle} />
                    </button>
                  </>
                )}
                
                {skill.status === "approved" && (
                  <button
                    onClick={() => handleStatusChange(skill.id, "flagged")}
                    className={`p-2 rounded transition-all duration-300 ${getActionButtonClasses("flag")}`}
                    title="Flag Skill"
                  >
                    <FontAwesomeIcon icon={faFlag} />
                  </button>
                )}
                
                {skill.status === "flagged" && (
                  <>
                    <button
                      onClick={() => handleStatusChange(skill.id, "approved")}
                      className={`p-2 rounded transition-all duration-300 ${getActionButtonClasses("approve")}`}
                      title="Approve Skill"
                    >
                      <FontAwesomeIcon icon={faCheckCircle} />
                    </button>
                    <button
                      onClick={() => handleStatusChange(skill.id, "rejected")}
                      className={`p-2 rounded transition-all duration-300 ${getActionButtonClasses("reject")}`}
                      title="Reject Skill"
                    >
                      <FontAwesomeIcon icon={faBan} />
                    </button>
                  </>
                )}
                
                {skill.status === "rejected" && (
                  <button
                    onClick={() => handleStatusChange(skill.id, "pending")}
                    className={`p-2 rounded transition-all duration-300 ${getActionButtonClasses("approve")}`}
                    title="Restore to Pending"
                  >
                    <FontAwesomeIcon icon={faUndo} />
                  </button>
                )}
                
                <button
                  className={`p-2 rounded transition-all duration-300 ${getActionButtonClasses("view")}`}
                  title="View Details"
                >
                  <FontAwesomeIcon icon={faEye} />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredSkills.length === 0 && (
        <div className="text-center py-12">
          <FontAwesomeIcon icon={faGraduationCap} className="text-6xl text-gray-300 mb-4" />
          <h3 className="text-xl font-medium text-gray-500 mb-2">No skills found</h3>
          <p className="text-gray-400">Try adjusting your search or filter criteria</p>
        </div>
      )}
    </div>
  );
};

export default SkillModeration;
